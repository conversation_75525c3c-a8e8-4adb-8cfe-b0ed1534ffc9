<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">训练参数配置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <LucideIcon
                name="ChevronDown"
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="flex flex-col space-y-2">
                <Label for="batchSize" class="text-sm font-medium">Batch Size</Label>
                <Input
                  id="batchSize"
                  v-model="localParams.batchSize"
                  type="number"
                  min="1"
                  max="1024"
                  placeholder="请输入批次大小"
                  class="h-10 w-full"
                  :disabled="props.disabled"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="epoch" class="text-sm font-medium">Epochs</Label>
                <Input
                  id="epoch"
                  v-model="localParams.epochs"
                  type="number"
                  min="1"
                  max="10000"
                  placeholder="请输入训练轮数"
                  class="h-10 w-full"
                  :disabled="props.disabled"
                />
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { LucideIcon } from '@renderer/components'

const trainParams = defineModel('trainParams', { type: Object, required: true })

// 接收 disabled 属性
const props = defineProps<{
  disabled?: boolean
}>()

// 创建本地响应式副本以减少父组件更新频率
const localParams = ref({ ...trainParams.value })

// 防抖更新父组件
const debouncedUpdate = useDebounceFn(() => {
  Object.assign(trainParams.value, localParams.value)
}, 300)

// 监听本地参数变化
watch(localParams, debouncedUpdate, { deep: true })

// 监听父组件参数变化（用于外部更新）
watch(
  trainParams,
  (newValue) => {
    if (JSON.stringify(newValue) !== JSON.stringify(localParams.value)) {
      localParams.value = { ...newValue }
    }
  },
  { deep: true },
)
</script>

<style scoped></style>
